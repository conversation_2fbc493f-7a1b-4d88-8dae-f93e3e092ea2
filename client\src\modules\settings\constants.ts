/**
 * Constants and configuration for the Settings module
 * Ultimate Electrical Designer - Settings & User Preferences
 */

import type {
  LanguageConfig,
  SettingsCategoryConfig,
  ThemeConfig,
  TimezoneConfig,
  UnitsConfig,
} from './types'

// Re-export the default values
export { DEFAULT_ENGINEERING_SETTINGS, DEFAULT_USER_PREFERENCES } from './types'

// Settings categories configuration
export const SETTINGS_CATEGORIES: SettingsCategoryConfig[] = [
  {
    id: 'account',
    label: 'Account',
    description: 'Profile, security, and authentication settings',
    icon: 'User',
    sections: [
      {
        id: 'profile',
        label: 'Profile Information',
        description: 'Basic profile settings',
        fields: [
          {
            id: 'first_name',
            label: 'First Name',
            type: 'text',
            value: '',
            validation: { required: true },
          },
          {
            id: 'last_name',
            label: 'Last Name',
            type: 'text',
            value: '',
            validation: { required: true },
          },
          {
            id: 'email',
            label: 'Email Address',
            type: 'text',
            value: '',
            validation: { required: true, pattern: '^[^@]+@[^@]+\.[^@]+$' },
          },
        ],
      },
      {
        id: 'security',
        label: 'Security',
        description: 'Password and security settings',
        fields: [
          {
            id: 'change_password',
            label: 'Change Password',
            type: 'text',
            value: '',
            description: 'Click to change your password',
          },
          {
            id: 'two_factor_enabled',
            label: 'Two-Factor Authentication',
            type: 'boolean',
            value: false,
            description: 'Enable two-factor authentication for enhanced security',
          },
        ],
      },
    ],
  },
  {
    id: 'appearance',
    label: 'Appearance',
    description: 'Theme, layout, and display preferences',
    icon: 'Palette',
    sections: [
      {
        id: 'theme',
        label: 'Theme',
        description: 'Color scheme and visual appearance',
        fields: [
          {
            id: 'theme',
            label: 'Color Theme',
            type: 'select',
            value: 'system',
            options: [
              { value: 'light', label: 'Light' },
              { value: 'dark', label: 'Dark' },
              { value: 'system', label: 'System Default' },
            ],
          },
          {
            id: 'dashboard_layout',
            label: 'Dashboard Layout',
            type: 'select',
            value: 'default',
            options: [
              { value: 'default', label: 'Default' },
              { value: 'compact', label: 'Compact' },
              { value: 'expanded', label: 'Expanded' },
            ],
          },
        ],
      },
      {
        id: 'display',
        label: 'Display',
        description: 'Display and formatting preferences',
        fields: [
          {
            id: 'language',
            label: 'Language',
            type: 'select',
            value: 'en',
            options: [
              { value: 'en', label: 'English' },
              { value: 'es', label: 'Español' },
              { value: 'fr', label: 'Français' },
              { value: 'de', label: 'Deutsch' },
            ],
          },
          {
            id: 'timezone',
            label: 'Timezone',
            type: 'select',
            value: 'UTC',
            options: [
              { value: 'UTC', label: 'UTC' },
              { value: 'Europe/London', label: 'London (GMT)' },
              { value: 'Europe/Paris', label: 'Paris (CET)' },
              { value: 'America/New_York', label: 'New York (EST)' },
            ],
          },
          {
            id: 'date_format',
            label: 'Date Format',
            type: 'select',
            value: 'YYYY-MM-DD',
            options: [
              { value: 'YYYY-MM-DD', label: '2025-07-17' },
              { value: 'DD/MM/YYYY', label: '17/07/2025' },
              { value: 'MM/DD/YYYY', label: '07/17/2025' },
              { value: 'DD-MM-YYYY', label: '17-07-2025' },
            ],
          },
          {
            id: 'time_format',
            label: 'Time Format',
            type: 'select',
            value: '24h',
            options: [
              { value: '24h', label: '24 Hour (14:30)' },
              { value: '12h', label: '12 Hour (2:30 PM)' },
            ],
          },
        ],
      },
    ],
  },
  {
    id: 'notifications',
    label: 'Notifications',
    description: 'Email, push, and desktop notification settings',
    icon: 'Bell',
    sections: [
      {
        id: 'general',
        label: 'General Notifications',
        fields: [
          {
            id: 'notifications_enabled',
            label: 'Enable Notifications',
            type: 'boolean',
            value: true,
            description: 'Receive notifications about important events',
          },
          {
            id: 'email_notifications',
            label: 'Email Notifications',
            type: 'boolean',
            value: true,
            description: 'Receive notifications via email',
          },
        ],
      },
    ],
  },
  {
    id: 'privacy',
    label: 'Privacy',
    description: 'Data sharing, analytics, and privacy settings',
    icon: 'Shield',
    sections: [
      {
        id: 'data',
        label: 'Data & Privacy',
        fields: [
          {
            id: 'analytics_enabled',
            label: 'Usage Analytics',
            type: 'boolean',
            value: true,
            description: 'Help improve the application by sharing anonymous usage data',
          },
          {
            id: 'crash_reporting',
            label: 'Crash Reporting',
            type: 'boolean',
            value: true,
            description: 'Automatically send crash reports to help fix issues',
          },
        ],
      },
    ],
  },
  {
    id: 'advanced',
    label: 'Advanced',
    description: 'Developer options, debugging, and advanced features',
    icon: 'Settings',
    sections: [
      {
        id: 'performance',
        label: 'Performance',
        fields: [
          {
            id: 'auto_save_enabled',
            label: 'Auto Save',
            type: 'boolean',
            value: true,
            description: 'Automatically save your work',
          },
          {
            id: 'auto_save_interval',
            label: 'Auto Save Interval (seconds)',
            type: 'number',
            value: 300,
            validation: { min: 30, max: 3600 },
            description: 'How often to automatically save (30-3600 seconds)',
          },
        ],
      },
      {
        id: 'developer',
        label: 'Developer Options',
        fields: [
          {
            id: 'debug_mode',
            label: 'Debug Mode',
            type: 'boolean',
            value: false,
            description: 'Enable debug logging and developer tools',
          },
          {
            id: 'api_logging',
            label: 'API Request Logging',
            type: 'boolean',
            value: false,
            description: 'Log all API requests to browser console',
          },
        ],
      },
    ],
  },
  {
    id: 'engineering',
    label: 'Engineering Calculations',
    description: 'Units, precision, standards, and calculation preferences',
    icon: 'Calculator',
    sections: [
      {
        id: 'units',
        label: 'Units & Measurements',
        fields: [
          {
            id: 'units_system',
            label: 'Units System',
            type: 'select',
            value: 'metric',
            options: [
              { value: 'metric', label: 'Metric (SI)' },
              { value: 'imperial', label: 'Imperial' },
            ],
          },
          {
            id: 'default_temperature_unit',
            label: 'Temperature Unit',
            type: 'select',
            value: '°C',
            options: [
              { value: '°C', label: 'Celsius (°C)' },
              { value: '°F', label: 'Fahrenheit (°F)' },
              { value: 'K', label: 'Kelvin (K)' },
            ],
          },
          {
            id: 'default_currency',
            label: 'Currency',
            type: 'select',
            value: 'EUR',
            options: [
              { value: 'EUR', label: 'Euro (€)' },
              { value: 'USD', label: 'US Dollar ($)' },
              { value: 'GBP', label: 'British Pound (£)' },
            ],
          },
        ],
      },
      {
        id: 'calculations',
        label: 'Calculation Settings',
        fields: [
          {
            id: 'calculation_precision',
            label: 'Decimal Precision',
            type: 'number',
            value: 2,
            validation: { min: 0, max: 6 },
            description: 'Number of decimal places for calculations (0-6)',
          },
          {
            id: 'safety_factor',
            label: 'Default Safety Factor',
            type: 'number',
            value: 1.2,
            validation: { min: 1.0, max: 3.0 },
            description: 'Default safety factor for calculations (1.0-3.0)',
          },
          {
            id: 'default_voltage',
            label: 'Default Voltage',
            type: 'select',
            value: '230V',
            options: [
              { value: '110V', label: '110V' },
              { value: '230V', label: '230V' },
              { value: '400V', label: '400V' },
              { value: '480V', label: '480V' },
            ],
          },
          {
            id: 'default_frequency',
            label: 'Default Frequency',
            type: 'select',
            value: '50Hz',
            options: [
              { value: '50Hz', label: '50 Hz' },
              { value: '60Hz', label: '60 Hz' },
            ],
          },
        ],
      },
      {
        id: 'standards',
        label: 'Standards & Compliance',
        fields: [
          {
            id: 'calculation_standards',
            label: 'Calculation Standards',
            type: 'multiselect',
            value: ['IEC', 'EN'],
            options: [
              { value: 'IEC', label: 'IEC (International)' },
              { value: 'EN', label: 'EN (European)' },
              { value: 'IEEE', label: 'IEEE (US)' },
              { value: 'BS', label: 'BS (British)' },
              { value: 'DIN', label: 'DIN (German)' },
            ],
          },
          {
            id: 'component_rating_system',
            label: 'Component Rating System',
            type: 'select',
            value: 'IP66',
            options: [
              { value: 'IP66', label: 'IP66 (European)' },
              { value: 'NEMA', label: 'NEMA (US)' },
            ],
          },
        ],
      },
    ],
  },
]

// Theme configurations
export const THEME_CONFIGS: Record<string, ThemeConfig> = {
  light: {
    name: 'light',
    label: 'Light',
    colors: {
      primary: 'oklch(0.21 0.006 285.885)',
      secondary: 'oklch(0.967 0.001 286.375)',
      background: 'oklch(1 0 0)',
      foreground: 'oklch(0.141 0.005 285.823)',
      muted: 'oklch(0.967 0.001 286.375)',
      accent: 'oklch(0.967 0.001 286.375)',
      destructive: 'oklch(0.637 0.237 25.331)',
      border: 'oklch(0.92 0.004 286.32)',
      input: 'oklch(0.871 0.006 286.286)',
      ring: 'oklch(0.21 0.006 285.885)',
    },
  },
  dark: {
    name: 'dark',
    label: 'Dark',
    colors: {
      primary: 'oklch(0.985 0 0)',
      secondary: 'oklch(0.141 0.005 285.823)',
      background: 'oklch(0.141 0.005 285.823)',
      foreground: 'oklch(0.985 0 0)',
      muted: 'oklch(0.21 0.006 285.885)',
      accent: 'oklch(0.21 0.006 285.885)',
      destructive: 'oklch(0.637 0.237 25.331)',
      border: 'oklch(0.21 0.006 285.885)',
      input: 'oklch(0.21 0.006 285.885)',
      ring: 'oklch(0.985 0 0)',
    },
  },
}

// Language configurations
export const LANGUAGE_CONFIGS: LanguageConfig[] = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
  },
  {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
  },
]

// Timezone configurations
export const TIMEZONE_CONFIGS: TimezoneConfig[] = [
  {
    value: 'UTC',
    label: 'UTC',
    offset: '+00:00',
    region: 'Universal',
  },
  {
    value: 'Europe/London',
    label: 'London',
    offset: '+00:00',
    region: 'Europe',
  },
  {
    value: 'Europe/Paris',
    label: 'Paris',
    offset: '+01:00',
    region: 'Europe',
  },
  {
    value: 'Europe/Berlin',
    label: 'Berlin',
    offset: '+01:00',
    region: 'Europe',
  },
  {
    value: 'America/New_York',
    label: 'New York',
    offset: '-05:00',
    region: 'North America',
  },
  {
    value: 'America/Los_Angeles',
    label: 'Los Angeles',
    offset: '-08:00',
    region: 'North America',
  },
  {
    value: 'Asia/Tokyo',
    label: 'Tokyo',
    offset: '+09:00',
    region: 'Asia',
  },
  {
    value: 'Asia/Shanghai',
    label: 'Shanghai',
    offset: '+08:00',
    region: 'Asia',
  },
]

// Units configurations
export const UNITS_CONFIGS: Record<string, UnitsConfig> = {
  metric: {
    system: 'metric',
    temperature: '°C',
    length: 'mm',
    weight: 'kg',
    pressure: 'bar',
    power: 'kW',
  },
  imperial: {
    system: 'imperial',
    temperature: '°F',
    length: 'in',
    weight: 'lb',
    pressure: 'psi',
    power: 'hp',
  },
}

// Storage and sync constants
export const STORAGE_KEYS = {
  SETTINGS_STORE: 'ued-settings-store',
  LAST_SYNC: 'ued-settings-last-sync',
  FORM_BACKUP: 'ued-settings-form-backup',
} as const

export const SYNC_CHANNELS = {
  SETTINGS: 'ued-settings-sync',
  PREFERENCES: 'ued-preferences-sync',
} as const

// API endpoints
export const API_ENDPOINTS = {
  PREFERENCES: '/api/v1/users/me/preferences',
  EXPORT: '/api/v1/users/me/preferences/export',
  IMPORT: '/api/v1/users/me/preferences/import',
  RESET: '/api/v1/users/me/preferences',
} as const

// Validation constants
export const VALIDATION_RULES = {
  AUTO_SAVE_INTERVAL: { min: 30, max: 3600 },
  CALCULATION_PRECISION: { min: 0, max: 6 },
  SAFETY_FACTOR: { min: 1.0, max: 3.0 },
  PASSWORD_MIN_LENGTH: 8,
  EMAIL_PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
} as const

// UI constants
export const UI_CONSTANTS = {
  SEARCH_DEBOUNCE_MS: 300,
  AUTO_SAVE_DEBOUNCE_MS: 1000,
  SYNC_INTERVAL_MS: 30000,
  TOAST_DURATION_MS: 3000,
  ANIMATION_DURATION_MS: 200,
} as const
